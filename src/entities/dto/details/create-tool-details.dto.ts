import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsEnum,
  IsUrl,
  IsBoolean,
  IsArray,
  IsString,
  IsEmail,
  IsObject,
  ArrayMinSize,
} from 'class-validator';
import { PricingModel, PriceRange, LearningCurve, TechnicalLevel } from 'generated/prisma';

export class CreateToolDetailsDto {
  @ApiPropertyOptional({ description: 'Learning curve for the tool' })
  @IsOptional()
  learning_curve?: LearningCurve;

  @ApiPropertyOptional({
    description: 'Key features of the tool as a list of strings',
    type: [String],
    example: ['Real-time collaboration', 'Advanced analytics'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  key_features?: string[];

  // Shared fields from Prisma schema
  @ApiPropertyOptional({
    description: 'Indicates if the tool has a free tier',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  has_free_tier?: boolean;

  @ApiPropertyOptional({
    description: 'List of use cases for the tool',
    type: [String],
    example: ['Data Analysis', 'Machine Learning'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  use_cases?: string[];

  @ApiPropertyOptional({
    description: 'Pricing model of the tool',
  })
  @IsOptional()
  pricing_model?: PricingModel;

  @ApiPropertyOptional({
    description: 'Price range of the tool',
    enum: PriceRange,
  })
  @IsOptional()
  @IsEnum(PriceRange)
  price_range?: PriceRange;

  @ApiPropertyOptional({ description: 'Detailed information about pricing' })
  @IsOptional()
  @IsString()
  pricing_details?: string;

  @ApiPropertyOptional({ description: 'URL to the pricing page' })
  @IsOptional()
  @IsUrl()
  pricing_url?: string;

  @ApiPropertyOptional({
    description: 'List of integrations with other tools/platforms',
    type: [String],
    example: ['Slack', 'Google Drive'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  integrations?: string[];

  @ApiPropertyOptional({ description: 'Email address for support' })
  @IsOptional()
  @IsEmail()
  support_email?: string;

  @ApiPropertyOptional({
    description: 'Indicates if live chat support is available',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  has_live_chat?: boolean;

  @ApiPropertyOptional({ description: 'URL to the community forum or page' })
  @IsOptional()
  @IsUrl()
  community_url?: string;

  // Added missing fields from EntityDetailsTool Prisma model
  @ApiPropertyOptional({ description: 'Programming languages used or supported', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  programming_languages?: string[];

  @ApiPropertyOptional({ description: 'Frameworks used or supported', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  frameworks?: string[];

  @ApiPropertyOptional({ description: 'Libraries used or supported', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  libraries?: string[];

  @ApiPropertyOptional({ description: 'Target audience for the tool', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  target_audience?: string[];

  @ApiPropertyOptional({ description: 'Deployment options available', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  deployment_options?: string[];

  @ApiPropertyOptional({ description: 'Supported operating systems', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  supported_os?: string[];

  @ApiPropertyOptional({ description: 'Indicates if mobile support is available' })
  @IsOptional()
  @IsBoolean()
  mobile_support?: boolean;

  @ApiPropertyOptional({ description: 'Indicates if API access is provided' })
  @IsOptional()
  @IsBoolean()
  api_access?: boolean;

  @ApiPropertyOptional({ description: 'Level of customization offered (e.g., Low, Medium, High)' })
  @IsOptional()
  @IsString()
  customization_level?: string;

  @ApiPropertyOptional({ description: 'Indicates if a free trial is available' })
  @IsOptional()
  @IsBoolean()
  trial_available?: boolean;

  @ApiPropertyOptional({ description: 'Indicates if a demo is available' })
  @IsOptional()
  @IsBoolean()
  demo_available?: boolean;

  @ApiPropertyOptional({ description: 'Indicates if the tool is open source' })
  @IsOptional()
  @IsBoolean()
  open_source?: boolean;

  @ApiPropertyOptional({ description: 'Support channels offered', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  support_channels?: string[];

  // Missing fields from EntityDetailsTool Prisma model
  @ApiPropertyOptional({
    description: 'Technical level required to use the tool',
    enum: TechnicalLevel,
    example: TechnicalLevel.INTERMEDIATE
  })
  @IsOptional()
  @IsEnum(TechnicalLevel)
  technical_level?: TechnicalLevel;

  @ApiPropertyOptional({
    description: 'Platforms supported by the tool',
    type: [String],
    example: ['Web', 'macOS', 'iOS', 'Android']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  platforms?: string[];

  @ApiPropertyOptional({
    description: 'Indicates if the tool provides an API',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  has_api?: boolean;
}